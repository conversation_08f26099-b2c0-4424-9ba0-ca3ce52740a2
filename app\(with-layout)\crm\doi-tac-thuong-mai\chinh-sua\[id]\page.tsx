'use client';

import {
    useGetPartnerDetail,
    useUpdatePartner,
} from '@/apis/partners/partners.api';
import { IPartnerPayload } from '@/apis/partners/partners.type';
import { KEYS_TO_PARTNER } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import {
    convertFormValueToPayload,
    convertPayloadToFormValue,
} from '@/utils/convert-data';
import { showToastSuccess } from '@/utils/toast-message';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import PartnerForm from '../../_components/PartnerForm';

export default function EditPartner() {
    const router = useRouter();
    const params = useParams();
    const id = params.id as string;
    const { data: partnerDetail } = useGetPartnerDetail(id);

    const { mutate: updatePartner } = useUpdatePartner({
        onSuccess: () => {
            showToastSuccess({
                title: 'Cập nhật thông tin đối tác thương mại thành công',
            });

            router.push(ROUTES.CRM.PARTNERS.INDEX);
        },
        onError: () => {
            toast.error('Chỉnh sửa đối tác thương mại thất bại');
        },
    });

    const handleCreatePartner = (data: IPartnerPayload) => {
        const contactIds = (data.contacts ?? []).map((contact) => contact.id);

        if (data.contacts) {
            delete data.contacts;
        }

        data.contactIds = contactIds;

        const payload = convertFormValueToPayload(data, KEYS_TO_PARTNER);

        updatePartner(payload as IPartnerPayload);
    };

    const handleCancel = () => {
        console.error('Error');
    };

    return (
        <PartnerForm
            initValue={
                convertPayloadToFormValue(partnerDetail) as IPartnerPayload
            }
            onSubmit={handleCreatePartner}
            onCancel={handleCancel}
        />
    );
}
