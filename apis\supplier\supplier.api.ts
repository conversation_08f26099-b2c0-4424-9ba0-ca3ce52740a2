import http, { ApiResponse, ApiResponseList } from '@/lib/apiBase';
import {
    ISupplier,
    ResponseSupplier,
    SearchSupplier,
    StatusSupplier,
} from './supplier.type';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';

const URI = '/api/v1.0/Product/suppliers';

export const supplierKey = {
    GET_LIST_SUPPLIER: 'GET_LIST_SUPPLIER',
    GET_DETAIL_SUPPLIER: 'GET_DETAIL_SUPPLIER',
    GET_RESTORE_SUPPLIER: 'GET_RESTORE_SUPPLIER',
};
export const supplierUri = {
    getListSupplier: `${URI}`,
    createSupplier: `${URI}`,
    updateSupplier: `${URI}/:id`,
    deleteSupplier: `${URI}`,
    getDetailSupplier: `${URI}/:id`,
    restoreSupplier: `${URI}/restore`,
    getListRestoreSupplier: `${URI}/restore`,
};

export const supplierApis = {
    getListSupplier: (params: SearchSupplier) => {
        return http.get<ApiResponseList<ResponseSupplier[]>>(
            supplierUri.getListSupplier,
            {
                params,
            },
        );
    },
    getDetailSupplier: (id: string) => {
        return http.get<ApiResponse<ISupplier>>(
            supplierUri.getDetailSupplier.replace(':id', id),
        );
    },
    createSupplier: (payload: ISupplier) => {
        return http.post<ApiResponse<ISupplier>>(
            supplierUri.createSupplier,
            payload,
        );
    },
    deleteSupplier: (ids: string[], isDeleted: boolean) => {
        return http.delete<StatusSupplier>(supplierUri.deleteSupplier, {
            data: ids,
            params: { isDeleted },
        });
    },
    updateSupplier: (payload: ISupplier) => {
        return http.put<ApiResponse<ISupplier>>(
            supplierUri.updateSupplier.replace(':id', payload.id),
            payload,
        );
    },
    restoreSupplier: (payload: string[]) => {
        return http.put<StatusSupplier>(supplierUri.restoreSupplier, payload);
    },
    getListRestore: (params: SearchSupplier) => {
        return http.get<ApiResponseList<ResponseSupplier[]>>(
            supplierUri.getListRestoreSupplier,
            {
                params,
            },
        );
    },
};

export const useSearchSupplier = (params: SearchSupplier) => {
    return useQuery({
        queryKey: [supplierKey.GET_LIST_SUPPLIER, params],
        queryFn: () => supplierApis.getListSupplier(params),
        select: (data) => data,
    });
};

export const useDetailSupplier = (id: string) => {
    return useQuery({
        queryKey: [supplierKey.GET_DETAIL_SUPPLIER, id],
        queryFn: () => supplierApis.getDetailSupplier(id),
        select: (data) => data,
    });
};

export const useListRestore = (params: SearchSupplier) => {
    return useQuery({
        queryKey: [supplierKey.GET_RESTORE_SUPPLIER, params],
        queryFn: () => supplierApis.getListRestore(params),
        select: (data) => data,
    });
};
export const useCreateSupplier = (props?: {
    onSuccess?: (data: ISupplier, response: ISupplier) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: ISupplier) =>
            supplierApis.createSupplier(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [supplierKey.GET_LIST_SUPPLIER],
            });
            onSuccess?.(variables, response.data);
        },
        onError,
    });
};

export const useDeleteSupplier = (props?: {
    onSuccess?: (
        data: { ids: string[]; isDeleted: boolean },
        response: StatusSupplier,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (params: { ids: string[]; isDeleted: boolean }) =>
            supplierApis.deleteSupplier(params.ids, params.isDeleted),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [supplierKey.GET_LIST_SUPPLIER],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};

export const useUpdateSupplier = (props?: {
    onSuccess?: (data: ISupplier, response: ISupplier) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: ISupplier) =>
            supplierApis.updateSupplier(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [supplierKey.GET_LIST_SUPPLIER],
            });
            onSuccess?.(variables, response.data);
        },
        onError,
    });
};

export const useRestoreSupplier = (props?: {
    onSuccess?: (data: string[], response: StatusSupplier) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: string[]) =>
            supplierApis.restoreSupplier(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [supplierKey.GET_RESTORE_SUPPLIER],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};
