'use client';

import { useParams } from 'next/navigation';
import FormSuppliers from '../../_components/FormSuppliers';
import { useDetailSupplier } from '@/apis/supplier/supplier.api';
import { Spinner } from 'reactstrap';
import { convertPayloadToFormValue } from '@/utils/convert-data';
import { ISupplier } from '@/apis/supplier/supplier.type';

const DetailSuppliers = () => {
    const params = useParams();
    const id = params.id as string;
    const { data: dataSuppliers, isLoading } = useDetailSupplier(id);
    if (isLoading) {
        return <Spinner />;
    }
    return (
        <FormSuppliers
            page='chi-tiet'
            initValue={
                convertPayloadToFormValue(dataSuppliers?.data) as ISupplier
            }
        />
    );
};
export default DetailSuppliers;
