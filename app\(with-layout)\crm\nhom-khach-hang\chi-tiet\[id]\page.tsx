'use client';

import {
    useDeleteCustomerGroups,
    useGetDetailCustomerGroups,
} from '@/apis/customer-groups/customer-groups.api';
import { ROUTES } from '@/lib/routes';
import { showToastSuccess } from '@/utils/toast-message';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    CardBody,
    CardHeader,
    Col,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Label,
    Row,
    UncontrolledDropdown,
} from 'reactstrap';
import DeleteConfirmModal from '../../_components/DeleteConfirmModal';
import Evaluate from '../evaluate';
import FileInfo from '../file-info';
import FormatTextDetail from '@/components/common/FormatTextDetail';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';

export default function BusinessDetails() {
    const params = useParams();
    const id = params.id as string;
    const router = useRouter();
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const { mutate: deleteCustomerGroups, isPending } = useDeleteCustomerGroups(
        {
            onSuccess: () => {
                showToastSuccess({
                    title: 'Xóa nhóm khách hàng thành công',
                });

                setIsDeleteModalOpen(false);
                router.push(ROUTES.CRM.PERSONAL.INDEX);
            },
            onError: () => {
                toast.error('Xóa nhóm khách hàng thất bại');
            },
        },
    );

    const handleConfirmDelete = () => {
        deleteCustomerGroups({ ids: [id] });
    };
    const { data } = useGetDetailCustomerGroups(id);
    const handlePut = () => {
        router.push(ROUTES.CRM.CUSTOMER_GROUPS.UPDATE.replace(':id', id));
    };
    return (
        <div>
            <Row>
                <Col lg={9}>
                    <Col lg={12}>
                        <Card>
                            <CardHeader style={{ borderBottom: 'none' }}>
                                <div
                                    className='d-flex align-items-center'
                                    style={{ padding: '0px 20px 0px 20px' }}
                                >
                                    <div className='flex-fill d-flex justify-content-start'>
                                        <h5 className='mb-0'>
                                            Thông tin chung
                                        </h5>
                                    </div>
                                    <div className='flex-fill d-flex justify-content-end align-items-center gap-2'>
                                        <Button
                                            className='d-flex justify-content-center align-items-center'
                                            style={{
                                                backgroundColor: 'white',
                                                color: '#0ab39c',
                                                borderColor: '#0ab39c',
                                                height: '30px',
                                                padding: '5px 8px 5px 8px',
                                            }}
                                            onClick={handlePut}
                                        >
                                            <i
                                                className=' ri-pencil-line'
                                                style={{
                                                    fontSize: '18px',
                                                    marginRight: '8px',
                                                }}
                                            ></i>
                                            Chỉnh sửa
                                        </Button>
                                        <UncontrolledDropdown>
                                            <DropdownToggle
                                                tag='button'
                                                className='btn'
                                                style={{
                                                    backgroundColor: '#0ab39c',
                                                    border: 'none',
                                                    padding: '4px',
                                                    minWidth: '30px',
                                                }}
                                            >
                                                <i
                                                    className='ri-more-fill'
                                                    style={{
                                                        color: 'white',
                                                    }}
                                                ></i>
                                            </DropdownToggle>
                                            <DropdownMenu end>
                                                <DropdownItem>
                                                    <i className='ri-user-received-line me-2'></i>
                                                    Bàn giao
                                                </DropdownItem>
                                                <DropdownItem>
                                                    <i className='ri-history-line me-2'></i>
                                                    Nhật ký hoạt động
                                                </DropdownItem>
                                                <DropdownItem
                                                    className='text-danger'
                                                    onClick={() =>
                                                        setIsDeleteModalOpen(
                                                            true,
                                                        )
                                                    }
                                                >
                                                    <i className='ri-delete-bin-line me-2'></i>
                                                    Xóa nhóm khách hàng
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </UncontrolledDropdown>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardBody>
                                <Row>
                                    <Col lg={12} className='mb-2'>
                                        <Row
                                            style={{
                                                padding: '0px 20px 0px 20px',
                                            }}
                                        >
                                            <Col lg={1}>
                                                <div className='relative inline-block'>
                                                    <div
                                                        className='flex items-center justify-center rounded-full font-bold d-flex justify-content-center align-items-center'
                                                        style={{
                                                            width: '60px',
                                                            height: '60px',
                                                            backgroundColor:
                                                                '#daf4f0',
                                                            color: '#0ab39c',
                                                            borderRadius: '50%',
                                                            fontSize: '20px',
                                                        }}
                                                    >
                                                        {data?.name?.charAt(0)}
                                                    </div>
                                                </div>
                                            </Col>
                                            <Col lg={7}>
                                                <div className='d-flex align-items-center h-100'>
                                                    <h5>{data?.name}</h5>
                                                </div>
                                            </Col>
                                        </Row>
                                    </Col>
                                    <Row
                                        style={{
                                            padding: '20px 40px 20px 40px',
                                        }}
                                    >
                                        <Col lg='4'>
                                            <FormatTextDetail
                                                label='Mã nhóm khách hàng'
                                                p={data?.code}
                                            />
                                        </Col>
                                        <Col lg='4'>
                                            <Label
                                                className='text-muted'
                                                style={{
                                                    fontSize: '13px',
                                                }}
                                            >
                                                Ngày tạo
                                            </Label>
                                            <p style={{ fontSize: '13px' }}>
                                                <FormattedDateTimeWithFormat
                                                    date={data?.createdOn}
                                                />
                                            </p>
                                        </Col>
                                        <Col lg='4'>
                                            <FormatTextDetail
                                                label='Nhân viên kinh doanh'
                                                p={data?.salePersonName}
                                            />
                                        </Col>
                                        <Col lg='12'>
                                            <FormatTextDetail
                                                label='Mô tả'
                                                p={data?.description}
                                            />
                                        </Col>
                                    </Row>
                                </Row>
                            </CardBody>
                        </Card>
                    </Col>
                    <Evaluate />
                </Col>
                {data && <FileInfo data={data} />}
            </Row>
            <DeleteConfirmModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                onConfirm={handleConfirmDelete}
                itemName={data?.name ? [data.name] : []}
                loading={isPending}
            />
        </div>
    );
}
