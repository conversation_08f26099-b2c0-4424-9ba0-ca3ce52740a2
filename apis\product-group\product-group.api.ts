import http, { ApiResponse, ApiResponseList } from '@/lib/apiBase';
import {
    IProductGroup,
    Parent,
    ResponseSearchProductGroup,
    SearchProductGroup,
    StatusProductGroup,
} from './product-group.type';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';

const URI = '/api/v1.0/Category';

export const productGroupsKey = {
    GET_LIST_PRODUCT_GROUPS: 'GET_LIST_PRODUCT_GROUPS',
    GET_DETAIL_PRODUCT_GROUPS: 'GET_DETAIL_PRODUCT_GROUPS',
    GET_PARENT: 'GET_PARENT',
};

export const productGroupsUri = {
    getListProductGroups: `${URI}/categories`,
    createProductGroups: `${URI}`,
    updateProductGroups: `${URI}/:id`,
    deleteProductGroups: `${URI}/multiple`,
    getDetailProductGroups: `${URI}/:id`,
    getParent: `${URI}/parents`,
    restoreProductGroups: `${URI}/restore`,
};

export const productGroupsApis = {
    getParents: () => {
        return http.get<Parent[]>(productGroupsUri.getParent);
    },
    getListProductGroups: (params: SearchProductGroup) => {
        return http.get<ApiResponseList<ResponseSearchProductGroup[]>>(
            productGroupsUri.getListProductGroups,
            {
                params,
            },
        );
    },
    getDetailProductGroups: (id: string) => {
        return http.get<ApiResponse<IProductGroup>>(
            productGroupsUri.getDetailProductGroups.replace(':id', id),
        );
    },
    createProductGroups: (payload: IProductGroup) => {
        return http.post<ApiResponse<IProductGroup>>(
            productGroupsUri.createProductGroups,
            payload,
        );
    },
    deleteProductGroups: (ids: string[], isDeleted: boolean) => {
        return http.delete<StatusProductGroup>(
            productGroupsUri.deleteProductGroups,
            {
                data: ids,
                params: { isDeleted },
            },
        );
    },
    updateProductGroups: (payload: IProductGroup) => {
        const id = payload.id ?? '';
        return http.put<ApiResponse<IProductGroup>>(
            productGroupsUri.updateProductGroups.replace(':id', id),
            payload,
        );
    },
    restoreProductGroups: (payload: string[]) => {
        return http.put<StatusProductGroup>(
            productGroupsUri.restoreProductGroups,
            payload,
        );
    },
};
export const useGetParent = () => {
    return useQuery({
        queryKey: [productGroupsKey.GET_PARENT],
        queryFn: () => productGroupsApis.getParents(),
        select: (data) => data,
    });
};

export const useSearchProductGroups = (params: SearchProductGroup) => {
    return useQuery({
        queryKey: [productGroupsKey.GET_LIST_PRODUCT_GROUPS, params],
        queryFn: () => productGroupsApis.getListProductGroups(params),
        select: (data) => {
            return data;
        },
        placeholderData: (previousData) => previousData,
    });
};

export const useDetailProductGroups = (id: string) => {
    return useQuery({
        queryKey: [productGroupsKey.GET_DETAIL_PRODUCT_GROUPS, id],
        queryFn: () => productGroupsApis.getDetailProductGroups(id),
        select: (data) => data,
    });
};

export const useCreateProductGroups = (props?: {
    onSuccess?: (data: IProductGroup, response: IProductGroup) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: IProductGroup) =>
            productGroupsApis.createProductGroups(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [productGroupsKey.GET_LIST_PRODUCT_GROUPS],
            });
            onSuccess?.(variables, response.data);
        },
        onError,
    });
};

export const useDeleteProductGroups = (props?: {
    onSuccess?: (
        data: { ids: string[]; isDeleted: boolean },
        response: StatusProductGroup,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};

    return useMutation({
        mutationFn: (params: { ids: string[]; isDeleted: boolean }) =>
            productGroupsApis.deleteProductGroups(params.ids, params.isDeleted),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [productGroupsKey.GET_LIST_PRODUCT_GROUPS],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};

export const useUpdateProductGroups = (props?: {
    onSuccess?: (data: IProductGroup, response: IProductGroup) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: IProductGroup) =>
            productGroupsApis.updateProductGroups(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [
                    productGroupsKey.GET_LIST_PRODUCT_GROUPS,
                    productGroupsKey.GET_DETAIL_PRODUCT_GROUPS,
                ],
            });
            // Invalidate specific detail query
            if (variables.id) {
                void queryClient.invalidateQueries({
                    queryKey: [productGroupsKey.GET_DETAIL_PRODUCT_GROUPS, variables.id],
                });
            }
            onSuccess?.(variables, response.data);
        },
        onError,
    });
};

export const useRestoreProductGroups = (props?: {
    onSuccess?: (data: string[], response: StatusProductGroup) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: string[]) =>
            productGroupsApis.restoreProductGroups(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [productGroupsKey.GET_LIST_PRODUCT_GROUPS],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};
