import { IDealDetail } from '@/apis/opportunity/opportunity.type';
import BoxAddInfo from '@/components/common/BoxAddInfo';
import { Button, Card, CardBody, CardHeader, Col } from 'reactstrap';

interface FileInfoProps {
    data?: IDealDetail;
}

const FileInfo = ({ data }: FileInfoProps) => {
    return (
        <Col lg={3}>
            <Card className='mb-3'>
                <CardHeader className='border-bottom-0'>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>File đính kèm (2)</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {[
                        {
                            name: 'CRM.docx',
                            icon: 'ri-file-text-line',
                            date: '11/01/2025',
                        },
                        {
                            name: 'CRM.ppt',
                            icon: 'ri-file-ppt-line',
                            date: '11/01/2025',
                        },
                    ].map((file, index) => (
                        <div className='mb-3' key={index}>
                            <div className='d-flex align-items-center mb-2'>
                                <i
                                    className={`${file.icon} text-primary fs-18 me-2`}
                                ></i>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-0'>{file.name}</h6>
                                    <small className='text-muted'>
                                        Ngày tải lên: {file.date}
                                    </small>
                                </div>
                                <div className='d-flex'>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon me-1'
                                    >
                                        <i className='ri-download-line'></i>
                                    </Button>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    <Button color='info' outline block>
                        Xem thêm
                    </Button>
                </CardBody>
            </Card>

            <BoxAddInfo
                title='Cơ hội'
                length={data?.deals?.length || 0}
                content={
                    data?.deals?.map((deal) => ({
                        title: deal?.title,
                        data: [
                            {
                                label: 'Doanh thu',
                                value: `${deal?.amount?.toLocaleString('vi-VN')} VNĐ`,
                                boxColor: false,
                            },
                            {
                                label: 'Hạn hoàn thành',
                                value: '-',
                                boxColor: false,
                            },
                            {
                                label: 'Xác suất thành',
                                value: `${deal?.probability}%`,
                                boxColor: false,
                            },
                        ],
                        // onViewQuick: () => {},
                        // onViewDetail: () => {},
                        // onDelete: () => {},
                    })) || []
                }
                // onAdd={() => {}}
            />

            <BoxAddInfo
                title='Đối tác thương mại'
                length={data?.tradePartners?.length || 0}
                content={
                    data?.tradePartners?.map((partner) => ({
                        title:
                            partner?.contacts && partner?.contacts?.length > 0
                                ? partner?.contacts?.[0]?.fullName
                                : 'FD Technologies',
                        data: [
                            {
                                label: 'Email',
                                value:
                                    partner?.email ||
                                    '<EMAIL>',
                                boxColor: false,
                            },
                            {
                                label: 'Số điện thoại',
                                value: partner?.phoneNumber || '0981827526',
                                boxColor: false,
                            },
                        ],
                        // onViewQuick: () => {},
                        // onViewDetail: () => {},
                        // onDelete: () => {},
                    })) || []
                }
                // onAdd={() => {}}
            />

            <BoxAddInfo
                title='Cá nhân'
                length={data?.contacts?.length || 0}
                content={
                    data?.contacts?.map((contact) => ({
                        title: contact?.fullName,
                        data: [
                            {
                                label: 'Email',
                                value: contact?.email || '<EMAIL>',
                                boxColor: false,
                            },
                            {
                                label: 'Số điện thoại',
                                value: contact?.phoneNumber || '0979273526',
                                boxColor: false,
                            },
                            {
                                label: 'Vai trò',
                                value:
                                    contact?.roleName ||
                                    'Người thẩm định kỹ thuật',
                                boxColor: true,
                            },
                        ],
                        // onViewQuick: () => {},
                        // onViewDetail: () => {},
                        // onDelete: () => {},
                    })) || []
                }
                // onAdd={() => {}}
            />

            <Card className='mb-3'>
                <CardHeader>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>Quy trình liên kết</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                        <Button
                            color='light'
                            size='sm'
                            className='btn-icon ms-1'
                        >
                            <i className='ri-more-2-line'></i>
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    <div className='mb-4'>
                        <h6 className='mb-3 fs-16'>{data?.process?.name}</h6>
                        <div className='mb-2'>
                            <span className='text-muted fs-14'>
                                Trạng thái:{' '}
                                <span
                                    className={`badge ${
                                        data?.process?.status === 1
                                            ? 'bg-success-subtle text-success'
                                            : data?.process?.status === 2
                                              ? 'bg-warning-subtle text-warning'
                                              : 'bg-danger-subtle text-danger'
                                    }`}
                                >
                                    {data?.process?.statusName}
                                </span>
                            </span>
                        </div>
                    </div>
                </CardBody>
            </Card>

            <BoxAddInfo
                title='Báo giá'
                length={data?.quotes?.length || 0}
                content={
                    data?.quotes?.map((quote) => ({
                        title: quote?.name,
                        data: [
                            {
                                label: 'Giá trị',
                                value: `${quote?.amount?.toLocaleString('vi-VN')} VNĐ`,
                                boxColor: false,
                            },
                            {
                                label: 'Ngày kết thúc',
                                value: new Date(
                                    quote?.closeDate,
                                ).toLocaleDateString('vi-VN'),
                                boxColor: false,
                            },
                        ],
                        // onViewQuick: () => {},
                        // onViewDetail: () => {},
                        // onDelete: () => {},
                    })) || []
                }
                // onAdd={() => {}}
            />

            <BoxAddInfo
                title='Hợp đồng'
                length={data?.contracts?.length || 0}
                content={
                    data?.contracts?.map((contract) => ({
                        title: contract?.name,
                        data: [
                            {
                                label: 'Ngày tạo',
                                value: `${new Date(contract?.createdDate).toLocaleDateString('vi-VN')} ${new Date(contract?.createdDate).toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`,
                                boxColor: false,
                            },
                            {
                                label: 'Người tạo',
                                value: contract?.creatorName,
                                boxColor: false,
                            },
                        ],
                        // onViewQuick: () => {},
                        // onViewDetail: () => {},
                        // onDelete: () => {},
                    })) || []
                }
                // onAdd={() => {}}
            />

            <Card className='mb-3'>
                <CardHeader>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>Dự án</h5>
                    </div>
                </CardHeader>
                <CardBody>
                    <div className='mb-4'>
                        <h6 className='mb-3 fs-16'>{data?.project?.name}</h6>

                        <div className='mb-2'>
                            <span className='text-muted fs-14'>
                                Người quản lý:{' '}
                                <span className='fw-medium text-dark'>
                                    {data?.project?.managerName}
                                </span>
                            </span>
                        </div>

                        <div className='mb-2'>
                            <span className='text-muted fs-14'>
                                Trạng thái dự án:{' '}
                                <span
                                    className={`fw-medium ${
                                        data?.project?.status === 1
                                            ? 'text-success'
                                            : data?.project?.status === 2
                                              ? 'text-warning'
                                              : 'text-danger'
                                    }`}
                                >
                                    {data?.project?.statusName}
                                </span>
                            </span>
                        </div>
                    </div>
                </CardBody>
            </Card>
        </Col>
    );
};

export default FileInfo;
