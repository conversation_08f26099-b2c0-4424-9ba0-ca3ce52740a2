import { DateTimeFormat } from '@/constants/format';
import dayjs from 'dayjs';

// Helper function to format display numbers
export const formatDisplayNumber = (value: number, decimals = 2) => {
    if (!isFinite(value) || isNaN(value)) {
        return '0';
    }

    // If number is too large, display in scientific notation or with abbreviation
    if (Math.abs(value) >= 1e12) {
        return (value / 1e12).toFixed(1) + 'T'; // Trillion
    }
    if (Math.abs(value) >= 1e9) {
        return (value / 1e9).toFixed(1) + 'B'; // Billion
    }
    if (Math.abs(value) >= 1e6) {
        return (value / 1e6).toFixed(1) + 'M'; // Million
    }

    return value.toLocaleString('vi-VN', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals,
    });
};
export const formatDateTime = (
    dateTimeString?: string,
    format = DateTimeFormat,
): string => {
    if (!dateTimeString) {
        return '';
    }
    return dayjs(dateTimeString).format(format);
};

// Format VNĐ
export const formatVND = (value: number, decimals = 0): string => {
    if (!isFinite(value) || isNaN(value)) {
        return '0 ₫';
    }

    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals,
    }).format(value);
};

// Format USD
export const formatUSD = (value: number, decimals = 2): string => {
    if (!isFinite(value) || isNaN(value)) {
        return '$0.00';
    }

    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals,
    }).format(value);
};
