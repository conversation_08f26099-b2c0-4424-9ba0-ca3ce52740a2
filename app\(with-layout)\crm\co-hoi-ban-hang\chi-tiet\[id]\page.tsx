'use client';
import { ROUTES } from '@/lib/routes';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

import { useGetDealDetail } from '@/apis/opportunity/opportunity.api';
import {
    Button,
    Card,
    CardBody,
    CardHeader,
    Col,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Nav,
    NavItem,
    NavLink,
    Row,
    TabContent,
    TabPane,
    UncontrolledDropdown,
} from 'reactstrap';
import Evaluate from '../evaluate';
import FileInfo from '../file-info';
import Tab1 from './Tab1';
import Tab2 from './Tab2';
import Tab3 from './Tab3';
import Tab4 from './Tab4';

const DetailDeals = () => {
    const params = useParams();
    const id = params.id as string;
    const { data } = useGetDealDetail({ id });
    const [activeTab, setActiveTab] = useState('1');
    const router = useRouter();

    const handlePut = () => {
        router.push(ROUTES.CRM.PARTNERS.UPDATE.replace(':id', id));
    };

    const toggleTab = (tab: string) => {
        if (activeTab !== tab) {
            setActiveTab(tab);
        }
    };

    return (
        <div>
            <Row>
                <Col lg={9}>
                    <Col lg={12}>
                        <Card>
                            <CardHeader className='border-0 d-flex justify-content-between align-items-center'>
                                <Nav tabs>
                                    <NavItem>
                                        <NavLink
                                            className={`px-3 py-2`}
                                            onClick={() => toggleTab('1')}
                                            style={{
                                                cursor: 'pointer',
                                                borderRadius: '0',
                                                border: 'none',
                                                transition: 'all 0.2s ease',
                                                fontWeight: 500,
                                                marginBottom: '-1px',
                                                backgroundColor: 'transparent',
                                                borderBottom:
                                                    activeTab === '1'
                                                        ? '2px solid #0ab39c'
                                                        : 'none',
                                                fontSize: '14px',
                                                color:
                                                    activeTab === '1'
                                                        ? '#0ab39c'
                                                        : '#212529',
                                            }}
                                        >
                                            Thông tin chung
                                        </NavLink>
                                    </NavItem>
                                    <NavItem>
                                        <NavLink
                                            className={`px-3 py-2`}
                                            onClick={() => toggleTab('2')}
                                            style={{
                                                cursor: 'pointer',
                                                borderRadius: '0',
                                                border: 'none',
                                                transition: 'all 0.2s ease',
                                                fontWeight: 500,
                                                marginBottom: '-1px',
                                                backgroundColor: 'transparent',
                                                borderBottom:
                                                    activeTab === '2'
                                                        ? '2px solid #0ab39c'
                                                        : 'none',
                                                fontSize: '14px',
                                                color:
                                                    activeTab === '2'
                                                        ? '#0ab39c'
                                                        : '#212529',
                                            }}
                                        >
                                            Thông tin cá nhân
                                        </NavLink>
                                    </NavItem>
                                    <NavItem>
                                        <NavLink
                                            className={`px-3 py-2`}
                                            onClick={() => toggleTab('3')}
                                            style={{
                                                cursor: 'pointer',
                                                borderRadius: '0',
                                                border: 'none',
                                                transition: 'all 0.2s ease',
                                                fontWeight: 500,
                                                marginBottom: '-1px',
                                                backgroundColor: 'transparent',
                                                borderBottom:
                                                    activeTab === '3'
                                                        ? '2px solid #0ab39c'
                                                        : 'none',
                                                fontSize: '14px',
                                                color:
                                                    activeTab === '3'
                                                        ? '#0ab39c'
                                                        : '#212529',
                                            }}
                                        >
                                            Nhu cầu khách hàng
                                        </NavLink>
                                    </NavItem>
                                    <NavItem>
                                        <NavLink
                                            className={`px-3 py-2`}
                                            onClick={() => toggleTab('4')}
                                            style={{
                                                cursor: 'pointer',
                                                borderRadius: '0',
                                                border: 'none',
                                                transition: 'all 0.2s ease',
                                                fontWeight: 500,
                                                marginBottom: '-1px',
                                                backgroundColor: 'transparent',
                                                borderBottom:
                                                    activeTab === '4'
                                                        ? '2px solid #0ab39c'
                                                        : 'none',
                                                fontSize: '14px',
                                                color:
                                                    activeTab === '4'
                                                        ? '#0ab39c'
                                                        : '#212529',
                                            }}
                                        >
                                            Thông tin sản phẩm
                                        </NavLink>
                                    </NavItem>
                                </Nav>
                                <div className='d-flex justify-content-center align-items-center gap-2'>
                                    <Button
                                        className='d-flex justify-content-center align-items-center'
                                        style={{
                                            backgroundColor: 'white',
                                            color: '#0ab39c',
                                            borderColor: '#0ab39c',
                                            height: '30px',
                                        }}
                                        onClick={handlePut}
                                    >
                                        <i className=' ri-pencil-line'></i>{' '}
                                        Chỉnh sửa
                                    </Button>
                                    <Button
                                        className='d-flex justify-content-center align-items-center'
                                        style={{
                                            backgroundColor: '#0ab39c',
                                            color: 'white',
                                            borderColor: '#0ab39c',
                                            height: '30px',
                                        }}
                                    >
                                        <i className='ri-add-line'></i> Tạo báo
                                        giá
                                    </Button>
                                    <UncontrolledDropdown>
                                        <DropdownToggle
                                            tag='button'
                                            className='btn'
                                            style={{
                                                backgroundColor: '#0ab39c',
                                                border: 'none',
                                                padding: '4px',
                                                minWidth: '30px',
                                            }}
                                        >
                                            <i
                                                className='ri-more-fill'
                                                style={{
                                                    color: 'white',
                                                }}
                                            ></i>
                                        </DropdownToggle>
                                        <DropdownMenu end>
                                            <DropdownItem>
                                                <i className='ri-user-received-line me-2'></i>
                                                Bàn giao cá nhân
                                            </DropdownItem>
                                            <DropdownItem>
                                                <i className='ri-history-line me-2'></i>
                                                Nhật ký hoạt động
                                            </DropdownItem>
                                            <DropdownItem className='text-danger'>
                                                <i className='ri-delete-bin-line me-2'></i>
                                                Xóa cá nhân
                                            </DropdownItem>
                                        </DropdownMenu>
                                    </UncontrolledDropdown>
                                </div>
                            </CardHeader>
                            <CardBody>
                                <TabContent
                                    activeTab={activeTab}
                                    className='p-3'
                                >
                                    <TabPane tabId='1'>
                                        <Tab1 data={data} />
                                    </TabPane>

                                    <TabPane tabId='2'>
                                        <Tab2 data={data} />
                                    </TabPane>

                                    <TabPane tabId='3'>
                                        <Tab3 data={data} />
                                    </TabPane>

                                    <TabPane tabId='4'>
                                        <Tab4 data={data} />
                                    </TabPane>
                                </TabContent>
                            </CardBody>
                        </Card>
                    </Col>
                    <Evaluate />
                </Col>

                <FileInfo data={data} />
            </Row>
        </div>
    );
};

export default DetailDeals;
