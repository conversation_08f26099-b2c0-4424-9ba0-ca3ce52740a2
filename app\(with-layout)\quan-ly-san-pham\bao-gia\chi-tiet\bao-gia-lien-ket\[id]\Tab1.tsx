import { IQuoteDetailResponse } from '@/apis/quotes/quotes.type';
import FormatTextDetail from '@/components/common/FormatTextDetail';
import { formatDateTime } from '@/utils/format';
import { Col, Label, Row } from 'reactstrap';

interface Tab1Props {
    data?: IQuoteDetailResponse;
}

const Tab1 = ({ data }: Tab1Props) => {
    const getAvatarText = (name?: string): string => {
        if (!name) {
            return '';
        }

        const words = name.trim().split(' ').filter(Boolean);

        if (words.length >= 2) {
            return (
                words[0].charAt(0) + words[words.length - 1].charAt(0)
            ).toUpperCase();
        } else if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        }

        return '';
    };
    return (
        <div>
            <div style={{ padding: '0px 20px 0px 20px' }}>
                <h3>{data?.name}</h3>
            </div>

            <Row className='mt-4' style={{ padding: '0px 20px 0px 20px' }}>
                <Col md={5}>
                    <Col md={12} className='mb-2'>
                        <FormatTextDetail
                            label='MÃ BÁO GIÁ'
                            p={data?.code || '-'}
                        />
                    </Col>
                    <Col md={12} className='mb-2'>
                        <FormatTextDetail
                            label='NGÀY BÁO GIÁ'
                            p={formatDateTime(data?.createdDateTime) || '-'}
                        />
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>KHÁCH HÀNG</Label>
                        <div className='d-flex align-items-center gap-2'>
                            <div
                                className='d-flex justify-content-center align-items-center rounded-circle'
                                style={{
                                    width: '30px',
                                    height: '30px',
                                    backgroundColor: '#daf4f0',
                                    color: '#0ab39c',
                                    fontSize: '14px',
                                    fontWeight: 'bold',
                                }}
                            >
                                {getAvatarText(data?.company?.name)}
                            </div>
                            <span>{data?.company?.name}</span>
                        </div>
                    </Col>
                    <Col md={12} className='mb-2'>
                        <FormatTextDetail
                            label='TÊN CƠ HỘI'
                            p={data?.dealName || '-'}
                        />
                    </Col>
                </Col>
                <Col md={5} className='mt-2'>
                    <Col md={12} className='mb-2'>
                        <FormatTextDetail
                            label='TRẠNG THÁI'
                            p={data?.statusName || '-'}
                        />
                    </Col>
                    <Col md={12} className='mb-2'>
                        <FormatTextDetail
                            label='NGÀY KẾT THÚC'
                            p={formatDateTime(data?.expectedEndDate) || '-'}
                        />
                    </Col>
                    <Col md={12} className='mb-2'>
                        <Label>NHÂN VIÊN KINH DOANH</Label>
                        <div className='d-flex align-items-center gap-2'>
                            <div
                                className='d-flex justify-content-center align-items-center rounded-circle'
                                style={{
                                    width: '30px',
                                    height: '30px',
                                    backgroundColor: '#daf4f0',
                                    color: '#0ab39c',
                                    fontSize: '14px',
                                    fontWeight: 'bold',
                                }}
                            >
                                {getAvatarText(data?.owner?.name)}
                            </div>
                            <span>{data?.owner?.name}</span>
                        </div>
                    </Col>
                </Col>
            </Row>
        </div>
    );
};
export default Tab1;
