'use client';

import { useParams, useRouter } from 'next/navigation';
import FormProducts from '../../_components/FormProducts';
import { useDetailProduct } from '@/apis/product/product.api';
import { Spinner, Alert, Button } from 'reactstrap';
import { ROUTES } from '@/lib/routes';

const DetailProducts = () => {
    const params = useParams();
    const router = useRouter();
    const id = params.id as string;
    const { data: product, isLoading, error } = useDetailProduct(id);
    const initValue = product?.data;

    if (isLoading) {
        return (
            <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
                <Spinner />
            </div>
        );
    }

    if (error) {
        return (
            <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
                <Alert color="danger">
                    <h4>Lỗi khi tải dữ liệu</h4>
                    <p>{error.message || 'Không thể tải thông tin sản phẩm'}</p>
                    <Button 
                        color="primary" 
                        onClick={() => router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.INDEX)}
                    >
                        Quay lại danh sách
                    </Button>
                </Alert>
            </div>
        );
    }

    if (!initValue) {
        return (
            <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
                <Alert color="warning">
                    <h4>Không tìm thấy sản phẩm</h4>
                    <p>Sản phẩm bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
                    <Button 
                        color="primary" 
                        onClick={() => router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.INDEX)}
                    >
                        Quay lại danh sách
                    </Button>
                </Alert>
            </div>
        );
    }

    return <FormProducts page='chi-tiet' initValue={initValue} />;
};

export default DetailProducts;
