'use client';

import { useCreateProduct } from '@/apis/product/product.api';
import { IProduct } from '@/apis/product/product.type';
import { KEYS_TO_PRODUCT } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import FormProducts from '../_components/FormProducts';
import { showToastSuccess } from '@/utils/toast-message';

const CreatProducts = () => {
    const router = useRouter();
    const { mutate: createProduct } = useCreateProduct({
        onSuccess: () => {
            showToastSuccess({
                title: 'Tạo mới sản phẩm thành công',
            });
            router.push(ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });
    const handleSubmit = (data: IProduct) => {
        data.productOptions.map((item) => {
            item.installationType = Number(item.installationType);
            item.productOptionType = Number(item.productOptionType);
        });
        data.commonStatus = 1;

        const payload = convertFormValueToPayload(data, KEYS_TO_PRODUCT);

        createProduct(payload as IProduct);
    };
    const handleClose = () => {
        router.back();
    };
    return (
        <FormProducts
            page='tao-moi'
            onSubmit={handleSubmit}
            onClose={handleClose}
        />
    );
};

export default CreatProducts;
