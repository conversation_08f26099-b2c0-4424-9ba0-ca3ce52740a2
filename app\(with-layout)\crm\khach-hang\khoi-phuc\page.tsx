'use client';
import {
    useRestoreCustomer,
    useSearchRestoreCustomer,
} from '@/apis/customer/customer.api';
import {
    SearchCustomerResponse,
    SearchRestores,
} from '@/apis/customer/customer.type';
import dynamic from 'next/dynamic';

import ButtonHeader from '@/components/common/ButtonHeader';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import { ROUTES } from '@/lib/routes';
import { getOneMonthAgo, getToday } from '@/utils/time';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Row,
} from 'reactstrap';
import useGetColumn from '../_hook/useGetColumn';
import { showToastSuccess } from '@/utils/toast-message';
import ModalRestore from '@/components/common/Modal/ModalRestore';
import ImportFileModal from '@/components/common/ImportFile';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then(
            (mod) => mod.default,
        ),
    {
        ssr: false,
    },
) as typeof import('@/components/common/MantineReactTable').default;
const RestoreCustomer = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const methods = useForm<SearchRestores>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });

    const { control, setValue } = methods;

    const [
        Name,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });

    const { mutate: restoreCustomers } = useRestoreCustomer({
        onSuccess: () => {
            showToastSuccess({
                title: 'Khôi phục khách hàng thành công',
            });
            refetch();
            setModal(false);
            setSelectedNames([]);
            refetch();
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    const handleRestoreCustomer = (
        customer: SearchCustomerResponse | undefined,
    ) => {
        if (customer) {
            restoreCustomers({ ids: [customer.id] });
        }
    };

    const handleRestoreSelected = () => {
        if (selectedIds.length > 0) {
            restoreCustomers({ ids: selectedIds });
        } else {
            toast.warning('Vui lòng chọn ít nhất một khách hàng để khôi phục');
        }
    };

    const columns = useGetColumn({
        page: 'restore',
        onRestore: handleRestoreCustomer,
    });

    const { data, refetch, isLoading } = useSearchRestoreCustomer({
        Name,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });

    // useEffect(() => {}, [
    //     Name,
    //     FromDate,
    //     ToDate,
    //     PageNumber,
    //     PageSize,
    //     SortField,
    //     IsDescending,
    // ]);

    // useEffect(() => {
    //     if (PageNumber && PageSize) {
    //         refetch();
    //     }
    // }, [PageNumber, PageSize, refetch]);

    const { items: listRestoreCustomer = [], totalItems } = data ?? {};
    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };
    const handleCreate = () => {
        router.push(ROUTES.CRM.CUSTOMERS.CREATE);
    };
    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col xl={12}>
                    <ButtonHeader
                        showDateFilters={true}
                        onCreateNew={handleCreate}
                        onImportExcel={() => setIsImportModalOpen(true)}
                    />
                </Col>
                <Col xl={12}>
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col lg={9}>
                                    <div className='d-flex '>
                                        <InputSearchNameWithApiControl
                                            name='Name'
                                            placeholder='Tìm kiếm theo tên khách hàng...'
                                        />
                                    </div>
                                </Col>
                                <Col lg={3}>
                                    <div className='d-flex justify-content-end gap-2'>
                                        <Button
                                            color='success'
                                            onClick={handleRestoreSelected}
                                            disabled={selectedIds.length === 0}
                                        >
                                            {selectedIds.length > 0
                                                ? `Khôi phục (${selectedIds.length})`
                                                : 'Khôi phục'}
                                        </Button>
                                        <Dropdown
                                            isOpen={dropdownOpen}
                                            toggle={toggleDropdown}
                                            direction='down'
                                        >
                                            <DropdownToggle
                                                outline
                                                className='settings-button'
                                                style={{
                                                    border: 'none',
                                                    backgroundColor: '#dff0fa',
                                                }}
                                            >
                                                <i className='ri-settings-2-line text-info'></i>
                                            </DropdownToggle>
                                            <DropdownMenu>
                                                <DropdownItem
                                                    onClick={() =>
                                                        router.push(
                                                            ROUTES.CRM.CUSTOMERS
                                                                .INDEX,
                                                        )
                                                    }
                                                >
                                                    Khách hàng
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </Dropdown>
                                    </div>
                                </Col>
                            </Row>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={listRestoreCustomer}
                            isLoading={isLoading}
                            totalItems={totalItems ?? 0}
                            onPageChange={(page: number) => {
                                setValue('PageNumber', page);
                            }}
                            onPageSizeChange={(size: number) => {
                                setValue('PageSize', size);
                            }}
                            tableProps={{
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },

                                state: {
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index =
                                                listRestoreCustomer.findIndex(
                                                    (
                                                        contact: SearchCustomerResponse,
                                                    ) => contact.id === id,
                                                );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                    pagination: {
                                        pageIndex:
                                            (PageNumber ? PageNumber : 1) - 1,
                                        pageSize: PageSize ? PageSize : 10,
                                    },
                                },

                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        listRestoreCustomer.findIndex(
                                                            (
                                                                contact: SearchCustomerResponse,
                                                            ) =>
                                                                contact.id ===
                                                                id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds: string[] = [];
                                    const newSelectedNames: string[] = [];
                                    Object.keys(selectedRows)
                                        .filter((key) => selectedRows[key])
                                        .forEach((key) => {
                                            const item =
                                                listRestoreCustomer[
                                                    parseInt(key)
                                                ];
                                            if (item) {
                                                newSelectedIds.push(item.id);
                                                newSelectedNames.push(
                                                    item.name,
                                                );
                                            }
                                        });

                                    setSelectedIds(newSelectedIds);
                                    setSelectedNames(newSelectedNames);
                                },
                                mantineTableBodyCellProps: {
                                    align: 'left',
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>
            <ModalRestore
                onRestore={handleRestoreSelected}
                onClose={handleClose}
                isOpen={modal}
                page='khách hàng'
                data={selectedNames}
            />
            <ImportFileModal
                isOpen={isImportModalOpen}
                toggle={() => setIsImportModalOpen(!isImportModalOpen)}
            />
        </FormProvider>
    );
};
export default RestoreCustomer;
