'use client';

import { useCreateSupplier } from '@/apis/supplier/supplier.api';
import { ISupplier } from '@/apis/supplier/supplier.type';
import { ROUTES } from '@/lib/routes';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import FormSuppliers from '../_components/FormSuppliers';
import { showToastSuccess } from '@/utils/toast-message';

const CreatSuppliers = () => {
    const router = useRouter();
    const { mutate: createSuppliers } = useCreateSupplier({
        onSuccess: () => {
            showToastSuccess({
                title: 'Tạo mới nhà cung cấp thành công',
            });
            router.push(ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });
    const handleSubmit = (data: ISupplier) => {
        createSuppliers(data);
    };
    const handleClose = () => {
        router.back();
    };
    return (
        <FormSuppliers
            page='tao-moi'
            onSubmit={handleSubmit}
            onClose={handleClose}
        />
    );
};
export default CreatSuppliers;
