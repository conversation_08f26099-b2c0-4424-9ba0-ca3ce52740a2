'use client';

import {
    useDeleteCustomer,
    useSearchCustomer,
} from '@/apis/customer/customer.api';
import {
    SearchCustomer,
    SearchCustomerResponse,
} from '@/apis/customer/customer.type';
import ButtonHeader from '@/components/common/ButtonHeader';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import SelectBusinessTypeControl from '@/components/common/FormController/SelectBusinessTypeControl';
import SelectIndustryControl from '@/components/common/FormController/SelectIndustryControl';
import ImportFileModal from '@/components/common/ImportFile';
import ModalDelete from '@/components/common/Modal/ModalDelete';
import { ROUTES } from '@/lib/routes';
import { getOneMonthAgo, getToday } from '@/utils/time';
import { MRT_ColumnDef } from 'mantine-react-table';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
} from 'reactstrap';
import useGetColumn from './_hook/useGetColumn';
import { ACTIONS } from './_types/action.type';
import { showToastSuccess } from '@/utils/toast-message';
const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<SearchCustomerResponse>,
        })),
    {
        ssr: false,
    },
);

const Customers = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);

    const handleSelectedAction = (
        action: ACTIONS,
        row: SearchCustomerResponse | undefined,
    ) => {
        if (!row) {
            console.error('No contact data provided');
            return;
        }
        switch (action) {
            case ACTIONS.DELETE:
                setSelectedIds([row.id]);
                setSelectedNames([row.name]);
                setModal(true);
                break;
            case ACTIONS.EDIT:
                router.push(ROUTES.CRM.CUSTOMERS.UPDATE.replace(':id', row.id));
                break;
            case ACTIONS.VIEW_DETAIL:
                router.push(ROUTES.CRM.CUSTOMERS.DETAIL.replace(':id', row.id));
                break;
            default:
                console.error('Action not found');
                break;
        }
    };

    const { mutate: deleteCustomer } = useDeleteCustomer({
        onSuccess: () => {
            showToastSuccess({
                title: 'Xóa khách hàng thành công',
            });

            setModal(false);
            setSelectedNames([]);
            setSelectedIds([]);
            refetch();
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    const handleConfimDelete = () => {
        deleteCustomer({
            ids: selectedIds,
        });
    };

    const columns = useGetColumn({
        onSelectedAction: handleSelectedAction,
        page: 'list-Customer',
    });

    const methods = useForm<SearchCustomer>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });
    const { control, setValue } = methods;

    const [
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'IndustryId',
            'BusinessType',
            'LeadStatus',
            'SalePerson',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });

    const { data, refetch, isLoading } = useSearchCustomer({
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });

    const { items: listCustomer = [], totalItems = 0 } = data ?? {};

    const handleCreate = () => {
        router.push(ROUTES.CRM.CUSTOMERS.CREATE);
    };
    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };
    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col xl={12}>
                    <ButtonHeader
                        showDateFilters={true}
                        onCreateNew={handleCreate}
                        onImportExcel={() => setIsImportModalOpen(true)}
                    />
                </Col>

                <Col lg={12}>
                    <Card>
                        <CardHeader>
                            <div className='d-flex flex-wrap align-items-center gap-2'>
                                <InputSearchNameWithApiControl
                                    name='Name'
                                    placeholder='Tìm kiếm theo tên nhóm khách hàng...'
                                />

                                <div className='d-flex gap-3'>
                                    <SelectBusinessTypeControl
                                        name='BusinessType'
                                        placeholder='Loại hình'
                                        style={{
                                            width: '250px',
                                        }}
                                        clearable={true}
                                    />
                                    <SelectIndustryControl
                                        name='IndustryId'
                                        businessTypeId={BusinessType ?? ''}
                                        placeholder='Lĩnh vực'
                                        style={{ width: '250px' }}
                                        clearable={true}
                                    />
                                </div>

                                <div className='d-flex gap-2 ms-auto'>
                                    {selectedIds.length > 0 && (
                                        <>
                                            <Button
                                                style={{
                                                    backgroundColor: 'red',
                                                    border: 'none',
                                                    color: 'white',
                                                }}
                                                onClick={() => {
                                                    setModal(true);
                                                }}
                                            >
                                                Xóa ({selectedIds.length})
                                            </Button>
                                        </>
                                    )}
                                    <Button
                                        outline
                                        className='filter-button'
                                        style={{
                                            border: 'none',
                                            backgroundColor: '#dff0fa',
                                        }}
                                    >
                                        <i className='ri-filter-line text-primary'></i>
                                    </Button>
                                    <Dropdown
                                        isOpen={dropdownOpen}
                                        toggle={toggleDropdown}
                                        direction='down'
                                    >
                                        <DropdownToggle
                                            outline
                                            className='settings-button'
                                            style={{
                                                border: 'none',
                                                backgroundColor: '#dff0fa',
                                            }}
                                        >
                                            <i className='ri-settings-2-line text-info'></i>
                                        </DropdownToggle>
                                        <DropdownMenu>
                                            <DropdownItem
                                                onClick={() =>
                                                    router.push(
                                                        ROUTES.CRM.CUSTOMERS
                                                            .RESTORE,
                                                    )
                                                }
                                            >
                                                Khôi phục
                                            </DropdownItem>
                                        </DropdownMenu>
                                    </Dropdown>
                                </div>
                            </div>
                        </CardHeader>

                        <MantineTable
                            columns={
                                columns as MRT_ColumnDef<SearchCustomerResponse>[]
                            }
                            data={listCustomer}
                            isLoading={isLoading}
                            totalItems={Number(totalItems)}
                            onPageChange={(page: number) => {
                                setValue('PageNumber', page);
                            }}
                            onPageSizeChange={(size: number) => {
                                setValue('PageSize', size);
                            }}
                            tableProps={{
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },

                                state: {
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index =
                                                listCustomer.findIndex(
                                                    (
                                                        contact: SearchCustomerResponse,
                                                    ) => contact.id === id,
                                                );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                    pagination: {
                                        pageIndex:
                                            (PageNumber ? PageNumber : 1) - 1,
                                        pageSize: PageSize ? PageSize : 10,
                                    },
                                },

                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        listCustomer.findIndex(
                                                            (
                                                                contact: SearchCustomerResponse,
                                                            ) =>
                                                                contact.id ===
                                                                id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds: string[] = [];
                                    const newSelectedNames: string[] = [];
                                    Object.keys(selectedRows)
                                        .filter((key) => selectedRows[key])
                                        .forEach((key) => {
                                            const item =
                                                listCustomer[parseInt(key)];
                                            if (item) {
                                                newSelectedIds.push(item.id);
                                                newSelectedNames.push(
                                                    item.name,
                                                );
                                            }
                                        });

                                    setSelectedIds(newSelectedIds);
                                    setSelectedNames(newSelectedNames);
                                },
                                mantineTableBodyCellProps: {
                                    align: 'left',
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>
            <ModalDelete
                onDelete={handleConfimDelete}
                onClose={handleClose}
                isOpen={modal}
                page='khách hàng'
                data={selectedNames}
            />
            <ImportFileModal
                isOpen={isImportModalOpen}
                toggle={() => setIsImportModalOpen(!isImportModalOpen)}
            />
        </FormProvider>
    );
};

export default Customers;
