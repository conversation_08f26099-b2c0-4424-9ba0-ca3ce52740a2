import http, { ApiResponse, ApiResponseList } from '@/lib/apiBase';
import {
    IProduct,
    ResponseSearchProduct,
    SearchProduct,
    StatusProduct,
} from './product.type';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';

const URI = '/api/v1.0/Product';

export const productKey = {
    GET_LIST_PRODUCT: 'GET_LIST_PRODUCT',
    GET_DETAIL_PRODUCT: 'GET_DETAIL_PRODUCT',
};
export const productUri = {
    getListProduct: `${URI}`,
    createProduct: `${URI}`,
    updateProduct: `${URI}/:id`,
    deleteProduct: `${URI}`,
    getDetailProduct: `${URI}/:id`,
    restoreProduct: `${URI}/restore`,
};

export const productApis = {
    getListProduct: (params: SearchProduct) => {
        return http.get<ApiResponseList<ResponseSearchProduct[]>>(
            productUri.getListProduct,
            {
                params,
            },
        );
    },
    getDetailProduct: (id: string) => {
        return http.get<ApiResponse<IProduct>>(
            productUri.getDetailProduct.replace(':id', id),
        );
    },
    createProduct: (payload: IProduct) => {
        return http.post<ApiResponse<IProduct>>(
            productUri.createProduct,
            payload,
        );
    },
    deleteProduct: (ids: string[], isDeleted: boolean) => {
        return http.delete<StatusProduct>(productUri.deleteProduct, {
            data: ids,
            params: { isDeleted },
        });
    },
    updateProduct: (payload: IProduct) => {
        const id = payload.id ?? '';
        return http.put<ApiResponse<IProduct>>(
            productUri.updateProduct.replace(':id', id),
            payload,
        );
    },
    restoreProduct: (payload: string[]) => {
        return http.put<StatusProduct>(productUri.restoreProduct, payload);
    },
};

export const useSearchProduct = (params: SearchProduct) => {
    return useQuery({
        queryKey: [productKey.GET_LIST_PRODUCT, params],
        queryFn: () => productApis.getListProduct(params),
        select: (data) => data,
        placeholderData: (previousData) => previousData,
    });
};

export const useDetailProduct = (id: string, o?: { enabled: boolean }) => {
    const enabled = o?.enabled ?? true;

    return useQuery({
        queryKey: [productKey.GET_DETAIL_PRODUCT, id],
        queryFn: () => productApis.getDetailProduct(id),
        select: (data) => data,
        enabled,
    });
};

export const useCreateProduct = (props?: {
    onSuccess?: (data: IProduct, response: IProduct) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: IProduct) => productApis.createProduct(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [productKey.GET_LIST_PRODUCT],
            });
            onSuccess?.(variables, response.data);
        },
        onError,
    });
};

export const useDeleteProduct = (props?: {
    onSuccess?: (
        data: { ids: string[]; isDeleted: boolean },
        response: StatusProduct,
    ) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (params: { ids: string[]; isDeleted: boolean }) =>
            productApis.deleteProduct(params.ids, params.isDeleted),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [productKey.GET_LIST_PRODUCT],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};

export const useUpdateProduct = (props?: {
    onSuccess?: (data: IProduct, response: IProduct) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: IProduct) => productApis.updateProduct(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [
                    productKey.GET_LIST_PRODUCT,
                    productKey.GET_DETAIL_PRODUCT,
                ],
            });
            // Invalidate specific detail query
            if (variables.id) {
                void queryClient.invalidateQueries({
                    queryKey: [productKey.GET_DETAIL_PRODUCT, variables.id],
                });
            }
            onSuccess?.(variables, response.data);
        },
        onError,
    });
};

export const useRestoreProduct = (props?: {
    onSuccess?: (data: string[], response: StatusProduct) => void;
    onError?: (error: AxiosError<null>) => void;
}) => {
    const queryClient = useQueryClient();
    const { onSuccess, onError } = props ?? {};
    return useMutation({
        mutationFn: (payload: string[]) => productApis.restoreProduct(payload),
        onSuccess: (response, variables) => {
            void queryClient.invalidateQueries({
                queryKey: [productKey.GET_LIST_PRODUCT],
            });
            onSuccess?.(variables, response);
        },
        onError,
    });
};
