'use client';
import {
    useDeleteContacts,
    useSearchContacts,
} from '@/apis/contact/contact.api';
import { IContactResponse, SearchContact } from '@/apis/contact/contact.type';
import ButtonHeader from '@/components/common/ButtonHeader';
import ComboboxSelectCumstomerControl from '@/components/common/FormController/ComboboxSelectCumstomerControl';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import SelectDepartmentControl from '@/components/common/FormController/SelectDepartmentControl';
import ImportFileModal from '@/components/common/ImportFile';
import ModalDelete from '@/components/common/Modal/ModalDelete';
import { ROUTES } from '@/lib/routes';
import { ACTIONS } from '@/types/actions.type';
import { getOneMonthAgo, getToday } from '@/utils/time';
import { showToastSuccess } from '@/utils/toast-message';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Row,
} from 'reactstrap';
import useGetColumn from './_hook/useGetColumn';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<IContactResponse>,
        })),
    {
        ssr: false,
    },
);
const Personal = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);

    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const methods = useForm<SearchContact>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: true,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });

    const { control, setValue } = methods;

    const [
        Name,
        CompanyId,
        DepartmentId,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'CompanyId',
            'DepartmentId',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });

    const { mutate: deleteContacts } = useDeleteContacts({
        onSuccess: () => {
            showToastSuccess({
                title: 'Xóa cá nhân thành công',
            });

            setModal(false);
            setSelectedNames([]);
            setSelectedIds([]);
            refetch();
        },
        onError: (error) => {
            toast.error(error.message || 'Xóa khách hàng thất bại');
        },
    });

    const handleSelectedAction = (action: ACTIONS, row?: IContactResponse) => {
        if (!row) {
            return;
        }
        switch (action) {
            case ACTIONS.DELETE:
                setSelectedIds([row.id]);
                setSelectedNames([row.name]);
                setModal(true);

                break;
            case ACTIONS.EDIT:
                router.push(ROUTES.CRM.PERSONAL.UPDATE.replace(':id', row.id));
                break;
            case ACTIONS.VIEW_DETAIL:
                router.push(ROUTES.CRM.PERSONAL.DETAIL.replace(':id', row.id));
                break;
            default:
                console.error('Action not found');
                break;
        }
    };

    const columns = useGetColumn({
        onSelectedAction: handleSelectedAction,
        page: 'list-contact',
    });

    const { data, refetch, isLoading } = useSearchContacts({
        Name,
        CompanyId,
        DepartmentId,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });

    const { items: listContact = [], totalItems = 0 } = data ?? {};

    const handleConfimDelete = () => {
        deleteContacts({ ids: selectedIds });
    };
    const onCreateNew = () => {
        router.push(ROUTES.CRM.PERSONAL.CREATE);
    };

    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };

    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col xl={12}>
                    <ButtonHeader
                        showDateFilters={true}
                        onCreateNew={onCreateNew}
                        onImportExcel={() => setIsImportModalOpen(true)}
                    />
                </Col>
                <Col xl={12}>
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col lg={9}>
                                    <div className='d-flex gap-3'>
                                        <InputSearchNameWithApiControl
                                            name='Name'
                                            placeholder='Tìm kiếm theo tên cá nhân...'
                                        />

                                        <ComboboxSelectCumstomerControl name='CompanyId' />

                                        <SelectDepartmentControl
                                            name='DepartmentId'
                                            placeholder='Phòng ban'
                                            style={{ width: '200px' }}
                                        />
                                    </div>
                                </Col>
                                <Col lg={3}>
                                    <div className='d-flex justify-content-end gap-2'>
                                        {selectedIds.length > 0 && (
                                            <>
                                                <Button
                                                    style={{
                                                        backgroundColor: 'red',
                                                        border: 'none',
                                                        color: 'white',
                                                    }}
                                                    onClick={() => {
                                                        setModal(true);
                                                    }}
                                                >
                                                    Xóa ({selectedIds.length})
                                                </Button>
                                            </>
                                        )}
                                        <Button
                                            outline
                                            className='filter-button'
                                            style={{
                                                border: 'none',
                                                backgroundColor: '#dff0fa',
                                            }}
                                        >
                                            <i className='ri-filter-line text-primary'></i>
                                        </Button>
                                        <Dropdown
                                            isOpen={dropdownOpen}
                                            toggle={toggleDropdown}
                                            direction='down'
                                        >
                                            <DropdownToggle
                                                outline
                                                className='settings-button'
                                                style={{
                                                    border: 'none',
                                                    backgroundColor: '#dff0fa',
                                                }}
                                            >
                                                <i className='ri-settings-2-line text-info'></i>
                                            </DropdownToggle>
                                            <DropdownMenu>
                                                <DropdownItem
                                                    onClick={() =>
                                                        router.push(
                                                            ROUTES.CRM.PERSONAL
                                                                .RESTORE,
                                                        )
                                                    }
                                                >
                                                    Khôi phục tài khoản
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </Dropdown>
                                    </div>
                                </Col>
                            </Row>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={listContact}
                            isLoading={isLoading}
                            totalItems={Number(totalItems)}
                            onPageChange={(page: number) => {
                                setValue('PageNumber', page);
                            }}
                            onPageSizeChange={(size: number) => {
                                setValue('PageSize', size);
                            }}
                            tableProps={{
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },

                                state: {
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index = listContact.findIndex(
                                                (contact: IContactResponse) =>
                                                    contact.id === id,
                                            );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                    pagination: {
                                        pageIndex:
                                            (PageNumber ? PageNumber : 1) - 1,
                                        pageSize: PageSize ? PageSize : 10,
                                    },
                                },

                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        listContact.findIndex(
                                                            (
                                                                contact: IContactResponse,
                                                            ) =>
                                                                contact.id ===
                                                                id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds: string[] = [];
                                    const newSelectedNames: string[] = [];
                                    Object.keys(selectedRows)
                                        .filter((key) => selectedRows[key])
                                        .forEach((key) => {
                                            const item =
                                                listContact[parseInt(key)];
                                            if (item) {
                                                newSelectedIds.push(item.id);
                                                newSelectedNames.push(
                                                    item.name,
                                                );
                                            }
                                        });

                                    setSelectedIds(newSelectedIds);
                                    setSelectedNames(newSelectedNames);
                                },
                                mantineTableBodyCellProps: {
                                    align: 'left',
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>

            <ModalDelete
                onDelete={handleConfimDelete}
                onClose={handleClose}
                isOpen={modal}
                page='cá nhân'
                data={selectedNames}
            />
            <ImportFileModal
                isOpen={isImportModalOpen}
                toggle={() => setIsImportModalOpen(!isImportModalOpen)}
            />
        </FormProvider>
    );
};

export default Personal;
