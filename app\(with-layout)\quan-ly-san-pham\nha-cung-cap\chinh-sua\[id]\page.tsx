'use client';

import {
    useDetailSupplier,
    useUpdateSupplier,
} from '@/apis/supplier/supplier.api';
import { ISupplier } from '@/apis/supplier/supplier.type';
import { ROUTES } from '@/lib/routes';
import { showToastSuccess } from '@/utils/toast-message';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { Spinner } from 'reactstrap';
import FormSuppliers from '../../_components/FormSuppliers';
import { convertPayloadToFormValue } from '@/utils/convert-data';

const UpdateSuppliers = () => {
    const params = useParams();
    const router = useRouter();
    const id = params.id as string;
    const { data: dataSuppliers, isLoading } = useDetailSupplier(id);

    const { mutate: updateSupplier } = useUpdateSupplier({
        onSuccess: () => {
            showToastSuccess({
                title: 'Cậ<PERSON> nhật thông tin nhà cung cấp thành công',
            });
            router.push(ROUTES.PRODUCT_MANAGEMENT.SUPPLIERS.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    const handleUpdate = (data: ISupplier) => {
        data.commonStatus = Number(data.commonStatus);
        updateSupplier(data);
    };

    const handleClose = () => {
        router.back();
    };

    if (isLoading) {
        return <Spinner />;
    }

    return (
        <FormSuppliers
            page='chinh-sua'
            initValue={
                convertPayloadToFormValue(dataSuppliers?.data) as ISupplier
            }
            onSubmit={handleUpdate}
            onClose={handleClose}
        />
    );
};
export default UpdateSuppliers;
