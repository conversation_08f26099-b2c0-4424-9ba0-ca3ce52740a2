'use client';
import { useCreatePartner } from '@/apis/partners/partners.api';
import { IPartnerPayload } from '@/apis/partners/partners.type';
import { KEYS_TO_PARTNER } from '@/constants/key-convert';
import { ROUTES } from '@/lib/routes';
import { convertFormValueToPayload } from '@/utils/convert-data';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import PartnerForm from '../_components/PartnerForm';
import { showToastSuccess } from '@/utils/toast-message';
// import { yupResolver } from '@hookform/resolvers/yup';
// import * as yup from 'yup';

// const schema = yup.object().shape({
//     name: yup.string().required('Vui lòng nhập tên doanh nghiệp'),
//     description: yup.string(),
//     website: yup.string(),
//     facebook: yup.string(),
//     youtube: yup.string(),
//     linkedInPage: yup.string(),
//     taxCode: yup.string(),
//     annualRevenue: yup.number().default(0),
//     numberOfEmployees: yup.number().default(0),
//     industryId: yup.string(),
//     CustomerType: yup.number().default(1),
//     dateOfEstablishment: yup.string(),
//     ownerId: yup.string(),
//     // Thông tin liên kết tạm thời
//     linkedPerson1: yup.string(),
//     linkedPerson2: yup.string(),
//     linkedRole1: yup.string(),
//     linkedRole2: yup.string(),
//     // Thông tin địa chỉ tạm thời
//     country: yup.string(),
//     district: yup.string(),
//     city: yup.string(),
//     ward: yup.string(),
//     address: yup.string(),
// });

export default function CreateCustomer() {
    const router = useRouter();

    const { mutate: createPartner } = useCreatePartner({
        onSuccess: () => {
            showToastSuccess({
                title: 'Tạo mới đối tác thương mại thành công',
            });

            router.push(ROUTES.CRM.PARTNERS.INDEX);
        },
        onError: () => {
            toast.error('Tạo mới đối tác thương mại thất bại');
        },
    });

    const handleCreatePartner = (data: IPartnerPayload) => {
        const contactIds = (data.contacts ?? []).map((contact) => contact.id);

        if (data.contacts) {
            delete data.contacts;
        }

        data.contactIds = contactIds;

        const payload = convertFormValueToPayload(data, KEYS_TO_PARTNER);

        createPartner(payload as IPartnerPayload);
    };

    const handleCancel = () => {
        console.error('Error');
    };

    return (
        <PartnerForm onSubmit={handleCreatePartner} onCancel={handleCancel} />
    );
}
