'use client';
import { ROUTES } from '@/lib/routes';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'react-toastify';

import {
    <PERSON>ton,
    Card,
    CardBody,
    CardHeader,
    Col,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Nav,
    NavItem,
    NavLink,
    Row,
    TabContent,
    TabPane,
    UncontrolledDropdown,
} from 'reactstrap';
import Evaluate from '../evaluate';
// import FileInfo from '../file-info';
import Tab1 from './Tab1';
import Tab2 from './Tab2';
import Tab3 from './Tab3';
import { useGetQuoteDetail, useDeleteQuotes } from '@/apis/quotes/quotes.api';
import FileInfo from '../file-info';
import { showToastSuccess } from '@/utils/toast-message';
import ModalDelete from '@/components/common/Modal/ModalDelete';

const DetailQuote = () => {
    const params = useParams();
    const id = params.id as string;
    const { data } = useGetQuoteDetail({ id });
    const [activeTab, setActiveTab] = useState('1');
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const router = useRouter();

    const { mutate: deleteQuote } = useDeleteQuotes({
        onSuccess: () => {
            showToastSuccess({
                title: 'Xóa báo giá thành công',
            });
            setIsDeleteModalOpen(false);
            router.push('/quan-ly-san-pham/bao-gia');
        },
        onError: (error) => {
            toast.error(error?.message || 'Xóa báo giá thất bại');
        },
    });

    const handlePut = () => {
        router.push(
            ROUTES.PRODUCT_MANAGEMENT.QUOTES.UPDATE_LINK_QUOTE.replace(
                ':id',
                id,
            ),
        );
    };

    const handleDelete = () => {
        setIsDeleteModalOpen(true);
    };

    const handleConfirmDelete = () => {
        deleteQuote({ ids: [id], isDeleted: false });
    };

    const handleCloseDeleteModal = () => {
        setIsDeleteModalOpen(false);
    };

    const toggleTab = (tab: string) => {
        if (activeTab !== tab) {
            setActiveTab(tab);
        }
    };

    return (
        <div>
            <Row>
                <Col lg={9}>
                    <Col lg={12}>
                        <Card>
                            <CardHeader className='border-0'>
                                <div className='d-flex justify-content-between align-items-end border-bottom'>
                                    <Nav tabs>
                                        <NavItem>
                                            <NavLink
                                                className={`px-3 py-2`}
                                                onClick={() => toggleTab('1')}
                                                style={{
                                                    cursor: 'pointer',
                                                    borderRadius: '0',
                                                    border: 'none',
                                                    transition: 'all 0.2s ease',
                                                    fontWeight: 500,
                                                    marginBottom: '-1px',
                                                    backgroundColor:
                                                        'transparent',
                                                    borderBottom:
                                                        activeTab === '1'
                                                            ? '2px solid #0ab39c'
                                                            : 'none',
                                                    fontSize: '14px',
                                                    color:
                                                        activeTab === '1'
                                                            ? '#0ab39c'
                                                            : '#212529',
                                                }}
                                            >
                                                Thông tin chung
                                            </NavLink>
                                        </NavItem>
                                        <NavItem>
                                            <NavLink
                                                className={`px-3 py-2`}
                                                onClick={() => toggleTab('2')}
                                                style={{
                                                    cursor: 'pointer',
                                                    borderRadius: '0',
                                                    border: 'none',
                                                    transition: 'all 0.2s ease',
                                                    fontWeight: 500,
                                                    marginBottom: '-1px',
                                                    backgroundColor:
                                                        'transparent',
                                                    borderBottom:
                                                        activeTab === '2'
                                                            ? '2px solid #0ab39c'
                                                            : 'none',
                                                    fontSize: '14px',
                                                    color:
                                                        activeTab === '2'
                                                            ? '#0ab39c'
                                                            : '#212529',
                                                }}
                                            >
                                                Thông tin khách hàng
                                            </NavLink>
                                        </NavItem>
                                        <NavItem>
                                            <NavLink
                                                className={`px-3 py-2`}
                                                onClick={() => toggleTab('3')}
                                                style={{
                                                    cursor: 'pointer',
                                                    borderRadius: '0',
                                                    border: 'none',
                                                    transition: 'all 0.2s ease',
                                                    fontWeight: 500,
                                                    marginBottom: '-1px',
                                                    backgroundColor:
                                                        'transparent',
                                                    borderBottom:
                                                        activeTab === '3'
                                                            ? '2px solid #0ab39c'
                                                            : 'none',
                                                    fontSize: '14px',
                                                    color:
                                                        activeTab === '3'
                                                            ? '#0ab39c'
                                                            : '#212529',
                                                }}
                                            >
                                                Thông tin hàng hóa
                                            </NavLink>
                                        </NavItem>
                                    </Nav>
                                    <div className='d-flex align-items-center gap-2 mb-1'>
                                        <Button
                                            className='d-flex justify-content-center align-items-center'
                                            style={{
                                                backgroundColor: '#ffffff',
                                                color: '#0ab39c',
                                                borderColor: '#0ab39c',
                                                height: '30px',
                                            }}
                                        >
                                            Gửi email
                                        </Button>
                                        <Button
                                            className='d-flex justify-content-center align-items-center'
                                            style={{
                                                backgroundColor: '#0ab39c',
                                                color: '#ffffff',
                                                borderColor: '#0ab39c',
                                                height: '30px',
                                            }}
                                        >
                                            Xác nhận
                                        </Button>
                                        <Button
                                            className='d-flex justify-content-center align-items-center'
                                            style={{
                                                backgroundColor: '#f06548',
                                                color: '#ffffff',
                                                borderColor: '#f06548',
                                                height: '30px',
                                            }}
                                        >
                                            Từ chối
                                        </Button>
                                        <Button
                                            className='d-flex justify-content-center align-items-center'
                                            style={{
                                                backgroundColor: 'white',
                                                color: '#0ab39c',
                                                borderColor: '#0ab39c',
                                                height: '30px',
                                            }}
                                            onClick={handlePut}
                                        >
                                            <i className=' ri-pencil-line'></i>{' '}
                                            Chỉnh sửa
                                        </Button>
                                        <Button
                                            className='d-flex justify-content-center align-items-center'
                                            style={{
                                                backgroundColor: '#0ab39c',
                                                color: 'white',
                                                borderColor: '#0ab39c',
                                                height: '30px',
                                            }}
                                        >
                                            <i className='ri-add-line'></i> Tạo
                                            báo giá
                                        </Button>
                                        <UncontrolledDropdown>
                                            <DropdownToggle
                                                tag='button'
                                                className='btn'
                                                style={{
                                                    backgroundColor: '#0ab39c',
                                                    border: 'none',
                                                    padding: '4px',
                                                    minWidth: '30px',
                                                }}
                                            >
                                                <i
                                                    className='ri-more-fill'
                                                    style={{
                                                        color: 'white',
                                                    }}
                                                ></i>
                                            </DropdownToggle>
                                            <DropdownMenu end>
                                                <DropdownItem>
                                                    <i className='ri-user-received-line me-2'></i>
                                                    Bàn giao
                                                </DropdownItem>
                                                <DropdownItem>
                                                    <i className='ri-history-line me-2'></i>
                                                    Nhật ký hoạt động
                                                </DropdownItem>
                                                <DropdownItem
                                                    className='text-danger'
                                                    onClick={handleDelete}
                                                >
                                                    <i className='ri-delete-bin-line me-2'></i>
                                                    Xóa
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </UncontrolledDropdown>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardBody>
                                <TabContent
                                    activeTab={activeTab}
                                    className='p-3'
                                >
                                    <TabPane tabId='1'>
                                        <Tab1 data={data?.data} />
                                    </TabPane>

                                    <TabPane tabId='2'>
                                        <Tab2 data={data?.data} />
                                    </TabPane>

                                    <TabPane tabId='3'>
                                        <Tab3 data={data?.data} />
                                    </TabPane>
                                </TabContent>
                            </CardBody>
                        </Card>
                    </Col>
                    <Evaluate />
                </Col>

                <FileInfo data={data?.data} />
            </Row>

            <ModalDelete
                onDelete={handleConfirmDelete}
                onClose={handleCloseDeleteModal}
                isOpen={isDeleteModalOpen}
                page=''
                data={[data?.data?.name || 'báo giá này']}
            />
        </div>
    );
};

export default DetailQuote;
